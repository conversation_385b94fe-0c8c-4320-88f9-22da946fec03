<!-- Full Screen Loading Overlay -->
<div *ngIf="isLoading" class="fullscreen-loading-overlay">
  <div class="loading-content">
    <div class="custom-colored-spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-4 text-primary fs-5">Loading...</div>
  </div>
</div>

<div class="project-view-container">
  <!-- Project Details Card -->
  <div class="card shadow-sm rounded-3" *ngIf="project">
    <!-- Project Details Header -->
    <div class="project-details-header">
      <div class="header-content">
        <div class="title-wrap">
          <div class="title-line">
            <span class="project-title">Project # {{ project.internalProjectNumber || "" }} - {{ project.projectName || "" }}</span>
            <span class="status-text status-active">{{ project.projectStatus || "Active" }}</span>
          </div>
        </div>
        <div class="button-group">
          <!-- <button type="button" class="btn portal-button" (click)="editProject()">
            <i class="fa fa-pencil"></i>Edit
          </button> -->
          <button type="button" class="btn btn-sm btn-light-primary d-flex align-items-center mb-2" (click)="goBack()">
            <i class="fas fa-arrow-left me-2"></i>
            Back
          </button>
        </div>
      </div>
    </div>
    <!-- Card Header with Tabs -->
    <div class="card-header border-0 py-2 d-flex justify-content-between align-items-center">
      <!-- Tabs -->
      <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-4 fw-bold flex-nowrap">
        <li class="nav-item">
          <a class="nav-link text-active-primary me-6 cursor-pointer" [ngClass]="{ active: selectedTab === 'details' }"
            (click)="showTab('details', $event)">
            Project details
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link text-active-primary me-6 cursor-pointer" [ngClass]="{ active: selectedTab === 'permits' }"
            (click)="showTab('permits', $event)">
            Permits list
          </a>
        </li>
      </ul>
       <div class="d-flex align-items-center gap-2" style="margin-right: 16px;">
        <!-- Edit icon - only show when permit details tab is active -->
        <button type="button" class="btn btn-link p-0" (click)="editProject()" *ngIf="selectedTab === 'details'" title="Edit Project">
          <i class="fas fa-edit text-primary" style="font-size: 1.1rem;"></i>
        </button>
      </div>
    </div>

    <!-- Card Body with Tab Content -->
    <div class="card-body">
      <!-- Project Details Tab Content -->
      <ng-container *ngIf="selectedTab == 'details' && project">
        <div class="project-details-content">
          <div class="project-details-grid">
            <div class="project-detail-item span-2">
           <label >Location</label>
              <span class="project-value">{{ project.projectLocation || "" }}</span>
            </div>
             <div class="project-detail-item">
               <label>Project manager</label>
               <span class="project-value">{{ project.internalProjectManagerName || project.internalProjectManager || "" }}</span>
             </div>
            <div class="project-detail-item">
              <label>External project manager</label>
              <span class="project-value">{{ project.externalPMNames || "" }}</span>
            </div>
            <div class="project-detail-item span-2">
              <label>Description</label>
              <span class="project-value">{{ project.projectDescription || "" }}</span>
            </div>
            <div class="project-detail-item">
              <label>Start date</label>
              <span class="project-value">{{
                project.projectStartDate
                ? (project.projectStartDate | date : "MM/dd/yyyy")
                : ""
              }}</span>
            </div>
            <div class="project-detail-item">
              <label>End date</label>
              <span class="project-value">{{
                project.projectEndDate
                ? (project.projectEndDate | date : "MM/dd/yyyy")
                : ""
              }}</span>
            </div>
           </div>
         </div>
       </ng-container>

      <!-- Permits List Tab Content -->
      <ng-container *ngIf="selectedTab == 'permits'">
        <!-- Empty State for Permits -->
        <div class="d-flex justify-content-center align-items-center py-5 text-muted" *ngIf="projectPermits.length === 0">
          <div class="text-center">
            <i class="fas fa-file-alt fa-3x mb-3"></i>
            <p>No permits found for this project.</p>
          </div>
        </div>

        <!-- Permits Table -->
        <div class="table-responsive" *ngIf="projectPermits.length > 0">
          <table class="table">
            <thead>
              <tr>
                <th>Permit Name</th>
                <th>Permit #</th>
                <th>Status</th>
                <th>Submitted Date</th>
                <th class="ball-in-court-col">Ball in Court</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let permit of projectPermits">
                <td>
                  <a 
                    class="fw-bold" 
                    (click)="viewPermit(permit.permitId)" 
                    title="View Permit"
                    aria-label="View Permit"
                  >
                    {{ permit.permitName || "" }}  
                  </a>
              <span class="badge badge-green-light ms-1">
  {{ permit.permitReviewType || "" }}
</span>
                </td>
                <td>
                  <span>{{ permit.permitNumber || "" }}</span>
                </td>
                <td>
                  <select class="form-select form-select-sm w-auto"
                          [value]="permit.internalReviewStatus || ''"
                          (change)="onStatusChange(permit, $any($event.target).value)"
                          [disabled]="isLoading">
                    <option [value]="''" disabled>Select status</option>
                    <option value="Approved">Approved</option>
                    <option value="Pacifica Verification">Pacifica Verification</option>
                    <option value="Dis-Approved">Dis-Approved</option>
                    <option value="Pending">Pending</option>
                    <option value="Not Required">Not Required</option>
                    <option value="In Review">In Review</option>
                    <option value="1 Cycle Completed">1 Cycle Completed</option>
                  </select>
                </td>
                <td>
                  <span>{{ permit.permitAppliedDate ? (permit.permitAppliedDate | date : 'MM/dd/yyyy') : '' }}</span>
                </td>
                <td class="ball-in-court-cell">
                  <span class="wrap-text">{{ permit.attentionReason || '' }}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </ng-container>
    </div>
  </div>
</div>
