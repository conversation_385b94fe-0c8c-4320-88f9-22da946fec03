.grid-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}
.search-section {
  .k-textbox {
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem; // reduced inner spacing for shorter height
    width: 80%; // full width (or set custom px)
    border: 2px solid #afc7dd; // highlight border color
    box-shadow: 0 0 6px rgba(59, 83, 135, 0.5); // glowing effect
  }

  .k-button {
    border-radius: 0.375rem;
    padding: 0.75rem 1.25rem; // bigger button
    min-width: 120px; // longer button
    background-color: #4c4e4f;
    color: white;
    font-weight: 500;
    transition: background 0.3s, transform 0.2s;

    &:hover {
      background-color: #4c4e4f;
      transform: scale(1.05);
    }
  }
}
/* Loading Overlay Styles handled globally in styles.scss */

.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 500;
  }

  .search-container {
    width: 300px;
  }
}

.grid-toolbar {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
}

/* Toolbar button styles */
.k-grid-toolbar {
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
    min-width: 40px;
    height: 40px;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Icon-only buttons - reduce padding for better icon centering */
    &:not(.btn-primary) {
      padding: 0.375rem;
      min-width: 40px;
      width: 40px;
    }

    /* Buttons with text - ensure consistent spacing */
    &.btn-primary {
      padding: 0.375rem 0.75rem;
      gap: 0.5rem;
    }
  }

  /* Kendo dropdown button styling for consistency */
  kendo-dropdownbutton {
    .k-button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      padding: 0.375rem 0.75rem;
      border-radius: 0.375rem;
      transition: all 0.15s ease-in-out;
      min-width: 40px;
      height: 40px;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }

  /* Custom dropdown button specific styling */
  .custom-dropdown {
    .k-button {
      background-color: #6f42c1;
      border-color: #6f42c1;
      color: white;

      &:hover {
        background-color: #5a32a3;
        border-color: #5a32a3;
      }
    }
  }

  .btn-success {
    background-color: #198754;
    border-color: #198754;
    color: white;

    &:hover {
      background-color: #157347;
      border-color: #146c43;
    }
  }

  .btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;

    &:hover {
      background-color: #ffca2c;
      border-color: #ffc720;
    }
  }

  .btn-info {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
    color: #000;

    &:hover {
      background-color: #31d2f2;
      border-color: #25cff2;
    }
  }

  .btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;

    &:hover {
      background-color: #5c636a;
      border-color: #565e64;
    }
  }
}

/* Search section styling */
.search-section {
  .kendo-textbox {
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;

    &:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }

  /* Kendo textbox specific styling */
  kendo-textbox {
    .k-textbox {
      border-radius: 0.375rem;
      border: 1px solid #dee2e6;
      transition: all 0.15s ease-in-out;

      &:hover {
        border-color: #adb5bd;
      }

      &:focus,
      &.k-state-focused {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    }
  }

  .k-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
    min-width: 40px;
    height: 40px;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

/* Total count styling */
.total-count {
  display: flex;
  align-items: center;
  font-size: 0.875rem;

  .text-muted {
    color: #6c757d !important;
  }

  .fw-bold {
    font-weight: 600 !important;
    color: #495057;
  }
}

.k-grid {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  font-size: 16px;
  color: #888;
  background-color: #f9f9f9;
  border-radius: 6px;
  margin-top: 20px;
}

.detail-container {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.detail-row {
  display: flex;
  margin-bottom: 8px;

  .detail-label {
    width: 120px;
    font-weight: 500;
    color: #666;
  }
}

/* Status indicators */
.status-active {
  padding: 4px 8px;
  background-color: #e8f5e9;
  color: #2e7d32;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-inactive {
  padding: 4px 8px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-pending {
  padding: 4px 8px;
  background-color: #fff8e1;
  color: #ff8f00;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* Custom Kendo UI styling */
:host ::ng-deep {
  .k-grid-header {
    background-color: #f5f5f5;
  }

  /* Action column icon consistency */
  .k-grid td .btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.15s ease-in-out;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  /* Refresh button styling */
  .k-button[title="Refresh"] {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 8px 12px;
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
      background-color: #e9ecef;
      border-color: #adb5bd;
      transform: scale(1.05);
    }

    .fas.fa-sync-alt {
      color: #6c757d;
      font-size: 14px;
    }
  }

  /* Column Configuration Panel */
  .column-config-panel {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white;

    .form-check {
      margin-bottom: 10px;

      .form-check-input {
        margin-right: 8px;

        &:checked {
          background-color: #28a745;
          border-color: #28a745;
        }
      }

      .form-check-label {
        color: white;
        font-weight: 500;
        cursor: pointer;
      }
    }

    .btn-primary {
      background-color: #28a745;
      border-color: #28a745;

      &:hover {
        background-color: #218838;
        border-color: #1e7e34;
      }
    }

    .btn-secondary {
      background-color: #6c757d;
      border-color: #6c757d;

      &:hover {
        background-color: #5a6268;
        border-color: #545b62;
      }
    }
  }

  /* Fixed columns styling */
  .k-grid-column-sticky {
    background-color: #f8f9fa !important;
    border-right: 2px solid #dee2e6 !important;

    .k-grid-header {
      background-color: #e9ecef !important;
    }
  }

  .k-grid-header th {
    font-weight: 600;
  }

  .k-pager-numbers .k-link.k-state-selected {
    background-color: #007bff;
    color: white;
  }

  .k-button {
    margin-right: 5px;
  }

  .k-icon {
    font-size: 16px;
  }

  .k-grid-content {
    overflow-y: auto;
  }

  .k-grid tr:hover {
    background-color: #f0f7ff;
  }

  .k-loading-mask {
    background-color: rgba(255, 255, 255, 0.7);
  }

  .k-loading-image::before,
  .k-loading-image::after {
    border-color: #007bff transparent;
  }
}

// ::ng-deep .k-grid td, .k-grid .k-table-td:hover{
//     background-color: white !important;
// }
::ng-deep .k-clear-value {
  // border-color: rgba(0, 0, 0, 0.08);
  color: red !important;
  // background-color: #ffffff;
}

::ng-deep .k-grid td,
::ng-deep .k-grid th {
  border: none !important; /* Remove borders from table cells and headers */
}

::ng-deep kendo-grid.k-grid .k-table-alt-row .k-grid-content-sticky {
  background-color: #fafafa !important;
}

::ng-deep kendo-grid.k-grid .k-grid-content-sticky {
  border-top-color: rgba(0, 0, 0, 0.08);
  border-left-color: rgba(0, 0, 0, 0.3);
  border-right-color: rgba(0, 0, 0, 0.3);
  background-color: #fafafa !important;
}

::ng-deep .k-grid .k-table-row.k-selected > td,
.k-grid td.k-selected,
.k-grid .k-table-row.k-selected > td,
.k-grid .k-table-td.k-selected,
.k-grid .k-table-row.k-selected > .k-table-td {
  background-color: transparent !important;
}
::ng-deep .k-grid .k-table-row.k-selected:hover .k-grid-content-sticky {
  background-color: #fafafa !important;
}
::ng-deep .k-clear-value {
  // border-color: rgba(0, 0, 0, 0.08);
  color: red !important;
  // background-color: #ffffff;
}

.fullscreen-grid {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
  padding: 20px;
  overflow: auto;
}

// Enhanced Advanced Filters Panel
.advanced-filters-panel {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  margin-bottom: 1rem;

  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
  }

  .k-dropdownlist {
    width: 100%;

    /* Consistent dropdown styling */
    .k-dropdown-wrap {
      border-radius: 0.375rem;
      border: 1px solid #dee2e6;
      transition: all 0.15s ease-in-out;

      &:hover {
        border-color: #adb5bd;
      }

      &:focus,
      &.k-state-focused {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    }

    .k-select {
      border-radius: 0 0.375rem 0.375rem 0;
    }
  }

  /* Advanced filters button consistency */
  .k-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
    min-width: 40px;
    height: 40px;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &.btn-primary {
      background-color: #007bff;
      border-color: #007bff;
      color: white;

      &:hover {
        background-color: #0056b3;
        border-color: #0056b3;
      }
    }

    &.btn-secondary {
      background-color: #6c757d;
      border-color: #6c757d;
      color: white;

      &:hover {
        background-color: #5c636a;
        border-color: #565e64;
      }
    }
  }
}

// Enhanced Grid Styles
:host ::ng-deep {
  .k-grid {
    .k-grid-header {
      background-color: #edf0f3;

      .k-header {
        font-weight: 600;
        color: #495057;
        border-color: #dee2e6;

        &:hover {
          background-color: #e9ecef;
        }
      }
    }

    .k-grid-content {
      .k-grid-row {
        &:hover {
          background-color: #f8f9fa;
        }

        &.k-alt {
          background-color: #f8f9fa;
        }
      }
    }

    .k-pager {
      background-color: #f8f9fa;
      border-top: 1px solid #dee2e6;
    }
  }

  // Enhanced Filter Menu
  .k-filter-menu {
    .k-filter-menu-content {
      padding: 1rem;

      .k-filter-menu-item {
        margin-bottom: 0.5rem;

        .k-textbox {
          width: 100%;
        }
      }
    }
  }

  // Enhanced Buttons
  .k-button {
    border-radius: 0.375rem;
    font-weight: 500;

    &.k-primary {
      background-color: #007bff;
      border-color: #007bff;

      &:hover {
        background-color: #0056b3;
        border-color: #0056b3;
      }
    }
  }
}

// Custom No Records
.custom-no-records {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
  font-style: italic;
}

// Responsive Design
@media (max-width: 768px) {
  .advanced-filters-panel {
    .row {
      .col-md-3 {
        margin-bottom: 1rem;
      }
    }
  }

  .k-grid-toolbar {
    flex-direction: column;
    align-items: stretch;

    .d-flex {
      margin-bottom: 1rem;
    }

    .kendo-grid-spacer {
      display: none;
    }
  }

  /* Column Selector Modal Styles */
  .column-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .column-item {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 8px;
    background-color: #fff;
    transition: all 0.2s ease;

    &:hover {
      border-color: #adb5bd;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &.dragging {
      opacity: 0.5;
      transform: rotate(5deg);
    }
  }

  .column-controls {
    display: flex;
    align-items: center;
    padding: 12px;
    gap: 12px;
  }

  .drag-handle {
    cursor: grab;
    color: #6c757d;

    &:active {
      cursor: grabbing;
    }
  }

  .column-checkbox {
    flex-shrink: 0;
  }

  .column-info {
    flex: 1;

    label {
      font-weight: 500;
      margin-bottom: 0;
      cursor: pointer;
    }

    small {
      font-size: 0.75rem;
    }
  }

  .column-actions {
    flex-shrink: 0;
  }
}

/* Additional Kendo UI consistency styles */
::ng-deep .k-grid-toolbar {
  /* Ensure all Kendo buttons have consistent sizing */
  .k-button {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0.375rem !important;
    transition: all 0.15s ease-in-out !important;
    min-width: 40px !important;
    height: 40px !important;

    &:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }
  }

  /* Kendo dropdown button specific styling */
  kendo-dropdownbutton {
    .k-button {
      padding: 0.375rem 0.75rem !important;
      gap: 0.5rem !important;
    }
  }

  /* Ensure consistent spacing between all toolbar elements */
  > * {
    margin-right: 0.5rem !important;

    &:last-child {
      margin-right: 0 !important;
    }
  }

  /* Consistent button margins */
  .btn,
  .k-button,
  kendo-dropdownbutton {
    margin-right: 0.5rem !important;

    &:last-child {
      margin-right: 0 !important;
    }
  }
}

/* Additional Kendo grid styling for consistency */
::ng-deep .k-grid {
  .k-grid-header {
    .k-header {
      background-color: #edf0f3 !important;
      font-weight: 600 !important;
      border-color: #dee2e6 !important;
    }
  }

  .k-grid-content {
    .k-table-row {
      &:hover {
        background-color: #f0f7ff !important;
      }
    }
  }

  .k-pager {
    .k-pager-numbers {
      .k-link {
        border-radius: 0.25rem !important;
        transition: all 0.15s ease-in-out !important;

        &:hover {
          background-color: #e9ecef !important;
        }

        &.k-state-selected {
          background-color: #007bff !important;
          color: white !important;
        }
      }
    }
  }
}
