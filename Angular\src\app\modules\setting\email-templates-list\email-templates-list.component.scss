// Navigation tabs styling
.nav-line-tabs {
  .nav-link {
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      color: #009ef7 !important;
      border-bottom: 2px solid #009ef7;
      font-weight: 600;
    }

    &:hover {
      color: #009ef7 !important;
    }
  }
}

// Tab content transitions
.tab-content {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.grid-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

/* Full Screen Loading Overlay */
.fullscreen-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.search-section {
  .k-textbox {
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem; // reduced inner spacing for shorter height
    width: 80%; // full width (or set custom px)
    border: 2px solid #afc7dd; // highlight border color
    box-shadow: 0 0 6px rgba(59, 83, 135, 0.5); // glowing effect
  }

  .k-button {
    border-radius: 0.375rem;
    padding: 0.75rem 1.25rem; // bigger button
    min-width: 120px; // longer button
    background-color: #4c4e4f;
    color: white;
    font-weight: 500;
    transition: background 0.3s, transform 0.2s;

    &:hover {
      background-color: #4c4e4f;
      transform: scale(1.05);
    }
  }
}

.loading-content {
  text-align: center;
  background-color: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-width: 200px;
}

.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 500;
  }

  .search-container {
    width: 300px;
  }
}

.grid-toolbar {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
}

/* Toolbar button styles */
.k-grid-toolbar {
  .btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
  }
}

/* Advanced filters panel */
.advanced-filters-panel {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin-bottom: 20px;
}

/* Statistics section */
.template-statistics {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin-bottom: 20px;

  .stat-item {
    padding: 15px;

    h4 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 5px;
    }

    small {
      font-size: 0.875rem;
      color: #6c757d;
    }
  }
}

/* Search section */
.search-section {
  .k-textbox {
    border-radius: 6px;
  }
}

// Match list page header/back-button alignment and sizing
.card-body > .d-flex.justify-content-between.align-items-center {
  align-items: center;
}

.back-button {
  display: inline-flex;
  align-items: center;
  align-self: center;
  padding: .15rem .5rem;
  border-radius: .55rem;
  font-weight: 600;
  font-size: .8rem;
  line-height: 1;
  margin-top: .1rem;
  i { font-size: .75rem; }
}

/* Badge styles */
.badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-success {
  background-color: #d4edda;
  color: #155724;
}

.badge-danger {
  background-color: #f8d7da;
  color: #721c24;
}

.badge-info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.badge-secondary {
  background-color: #e2e3e5;
  color: #383d41;
}

/* Button group styles */
.btn-group-sm .btn {
  padding: 4px 8px;
  font-size: 0.875rem;
  border-radius: 4px;
}

/* Custom no records template */
.custom-no-records {
  padding: 40px;
  text-align: center;

  .spinner-border {
    width: 2rem;
    height: 2rem;
  }
}

/* Bulk actions section */
.bulk-actions {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 15px;
}

/* Fullscreen grid container when expanded */
.fullscreen-grid {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: #ffffff;
  padding: 20px;
  overflow: auto;
}