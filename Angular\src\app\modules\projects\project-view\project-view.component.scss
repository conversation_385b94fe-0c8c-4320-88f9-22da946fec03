.project-view-container {
  display: flex;
  flex-direction: column;
  gap: 0;
}

// Reduce space between the tabs header and content body
.project-view-container {
  .card-header {
    margin-bottom: 0;
    padding-bottom: 0.25rem; // tighter header bottom spacing
  }

  .card-body {
    padding-top: 0.5rem; // tighter body top spacing
  }
}

// Project Details Header
.project-details-header {
  padding: 0 1.5rem;
  border-bottom: 1px solid #e5eaee;
  background: transparent;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.5rem;
  }

  h4 {
    margin: 0;
    color: #3f4254;
    font-weight: 600;
    font-size: 1.1rem;
  }

  .button-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;

    .btn-sm {
      font-size: 0.875rem !important;
      padding: 0.375rem 0.75rem !important;
      line-height: 1.5 !important;
    }
  }

  .back-button,
  .portal-button {
    display: inline-flex;
    align-items: center;
    gap: .3rem;
    padding: .15rem .5rem;
    border-radius: .55rem;
    background-color: #f3f6f9;
    color: #3f4254;
    border: 1px solid #e5eaee;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    font-weight: 600;
    font-size: .8rem;
    line-height: 1;
    transition: background-color .2s ease, box-shadow .2s ease, transform .02s ease;

    i {
      color: #5e6e82;
      font-size: .75rem;
    }

    &:hover {
      background-color: #eef2f7;
      box-shadow: 0 3px 10px rgba(0,0,0,0.07);
      text-decoration: none;
    }

    &:active {
      transform: translateY(1px);
      box-shadow: 0 1px 4px rgba(0,0,0,0.06);
    }
  }

  .portal-button {
    i {
      margin-right: 0.25rem;
    }
  }
}

.project-details-content {
  padding: 1rem 1.5rem;
}

.project-details-grid {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 1.5rem;
}

.project-detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;

  label {
      font-weight: 600;     /* bolder label */
  display: block;       /* ensures it sits above */
  margin-bottom: 0.15rem; 
  font-size: 15px;
    display: block;
    line-height: 1.2; 
    // font-size: 0.875rem;
    // font-weight: 600;
    // color: #6c7293;
    // // text-transform: uppercase;
    // letter-spacing: 0.1rem;
    // margin: 0;
  }

  .project-value,
  .project-status {
    margin-top: 0.1rem;
  }

 .project-label {
  font-weight: 600;     /* bolder label */
  display: block;       /* ensures it sits above */
  margin-bottom: 0.25rem; /* reduce space below label */
}
.status-text {
  font-size: .9rem;
  font-weight: 600;
  color: #3f4254;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid transparent;
  width: fit-content;

  // Status color variations for permit status
  &.status-approved {
    background-color: #e8f5e8;
    color: #1b5e20;
    border: 1px solid #c8e6c9;
  }

  &.status-pending {
    background-color: #fff3e0;
    color: #e65100;
    border: 1px solid #ffcc02;
  }

  &.status-under-review {
    background-color: #e8eaf6;
    color: #3949ab;
    border: 1px solid #c5cae9;
  }

  &.status-rejected {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
  }

  &.status-submitted {
    background-color: #e3f2fd;
    color: #1565c0;
    border: 1px solid #bbdefb;
  }

  &.status-requires-resubmit {
    background-color: #fff8e1;
    color: #f57f17;
    border: 1px solid #ffecb3;
  }

  &.status-conditional-approval {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #e1bee7;
  }

  &.status-void {
    background-color: #f5f5f5;
    color: #757575;
    border: 1px solid #e0e0e0;
  }

  &.status-n-a {
    background-color: #f5f5f5;
    color: #757575;
    border: 1px solid #e0e0e0;
  }
}
.badge-green-light {
  background-color: #42c761;  /* light green */
  color: #155724;             /* darker text for contrast */
}
.project-value {
  font-size: 1rem;
  color: #3f4254;
  font-weight: 500;
  margin: 0;           /* remove extra spacing */
  padding: 0.25rem 0;  /* tighter spacing */
  border-bottom: none;
}

  .project-status {
    display: block;
    vertical-align: top;
    padding: 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    text-align: left;
    background: transparent;
    border: none;
    min-width: 0;
    border-radius: 0;
  }
}

// Allow select items to span two columns in the grid
.project-detail-item.span-2 {
  grid-column: span 2;
}

// Status color variations
.status-active {
  background-color: #e8f5e8;
  color: #1b5e20;
  border: 1px solid #c8e6c9;
}

.status-inactive {
  background-color: #f5f5f5;
  color: #757575;
  border: 1px solid #e0e0e0;
}

.status-completed {
  background-color: #e8f5e8;
  color: #1b5e20;
  border: 1px solid #c8e6c9;
}

.status-pending {
  background-color: #fff3e0;
  color: #e65100;
  border: 1px solid #ffcc02;
}

.status-cancelled {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ffcdd2;
}

.status-n-a {
  background-color: #f5f5f5;
  color: #757575;
  border: 1px solid #e0e0e0;
}

// Table Styling for Permits
.table-responsive {
  overflow-x: auto;
  padding: 0 0.5rem 0 0.5rem;
  margin-top: 0;
}

.table {
  margin-bottom: 0;
  overflow: hidden;
  table-layout: fixed;
  width: 100%;

  thead {
    background-color: #f8f9fa;
    
    th {
      font-weight: 600;
      color: #3f4254;
      border-bottom: 2px solid #e5eaee;
      padding: 1rem 0.75rem;
    }
  }

  tbody {
    tr {
      transition: none;
      border-bottom: 1px solid #e5eaee;
    }

    td {
      padding: 0.75rem;
      vertical-align: middle;
      white-space: nowrap; // keep single-line rows
    }
  }

  // Column width adjustments for permits list
  thead th.permit-number-col { width: 25%; }
  thead th.permit-description-col { width: 40%; }
  thead th.permit-type-col { width: 20%; }
  thead th.permit-status-col { width: 15%; }

  tbody td.permit-number-cell { width: 25%; white-space: nowrap; }
  tbody td.permit-description-cell { width: 40%; }
  tbody td.permit-type-cell { width: 20%; }
  tbody td.permit-status-cell { width: 15%; }

  // New: widen first column (Permit Name) and show pointer on hover
  thead th:first-child { width: 30%; }
  tbody td:first-child { width: 30%; }
  tbody tr td:first-child,
  tbody tr td:first-child a {
    cursor: pointer;
  }
  // Remove link underline on row hover
  tbody tr:hover td:first-child a { text-decoration: none; }

  // Allow wrapping for Ball in Court column
  thead th.ball-in-court-col { width: 25%; }
  tbody td.ball-in-court-cell {
    white-space: normal;
    word-break: break-word;
    overflow-wrap: anywhere;
  }
}

.permit-number-cell {
  .permit-number-link {
    color: var(--bs-primary, #0d6efd);
    text-decoration: none;
    font-weight: 700;
    cursor: pointer;
    transition: color 0.15s ease, text-decoration 0.15s ease;

    &:hover {
      color: #0b5ed7;
      text-decoration: underline;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(24, 125, 228, 0.25);
      border-radius: 0.25rem;
    }
  }
}

.permit-status-cell {
  .status-text {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 600;
    border: 1px solid transparent;
    text-transform: uppercase;
    letter-spacing: 0.05rem;

    // Status color variations for permit status
    &.status-approved {
      background-color: #e8f5e8;
      color: #1b5e20;
      border: 1px solid #c8e6c9;
    }

    &.status-pending {
      background-color: #fff3e0;
      color: #e65100;
      border: 1px solid #ffcc02;
    }

    &.status-under-review {
      background-color: #e8eaf6;
      color: #3949ab;
      border: 1px solid #c5cae9;
    }

    &.status-rejected {
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #ffcdd2;
    }

    &.status-submitted {
      background-color: #e3f2fd;
      color: #1565c0;
      border: 1px solid #bbdefb;
    }

    &.status-requires-resubmit {
      background-color: #fff8e1;
      color: #f57f17;
      border: 1px solid #ffecb3;
    }

    &.status-conditional-approval {
      background-color: #f3e5f5;
      color: #7b1fa2;
      border: 1px solid #e1bee7;
    }

    &.status-void {
      background-color: #f5f5f5;
      color: #757575;
      border: 1px solid #e0e0e0;
    }

    &.status-complete {
      background-color: #e8f5e8;
      color: #1b5e20;
      border: 1px solid #c8e6c9;
    }

    &.status-approved-w-conditions {
      background-color: #f3e5f5;
      color: #7b1fa2;
      border: 1px solid #e1bee7;
    }

    &.status-requires-re-submit {
      background-color: #fff8e1;
      color: #f57f17;
      border: 1px solid #ffecb3;
    }

    &.status-unknown {
      background-color: #f5f5f5;
      color: #757575;
      border: 1px solid #e0e0e0;
    }

    &.status-n-a {
      background-color: #f5f5f5;
      color: #757575;
      border: 1px solid #e0e0e0;
    }
  }
}

// Truncate overflowing text with ellipsis
.permit-description-cell span,
.permit-type-cell span,
.permit-status-cell span {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.permit-actions-cell {
  .btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 600;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
}


.title-wrap {
  display: flex;
  flex-direction: column;
  gap: .25rem;
}

.title-line {
  display: flex;
  align-items: baseline;
  gap: .75rem;
}

.project-title {
  font-size: 1.05rem;
  font-weight: 700;
  color: #181c32;
}

.status-text {
  font-size: .9rem;
  font-weight: 600;
  color: #3f4254;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid transparent;
  width: fit-content;
}

.project-number-line {
  font-size: .85rem;
  color: #6c7293;
  padding-bottom: .25rem;
}

// Full Screen Loading Overlay
.fullscreen-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  .loading-content {
    text-align: center;
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .project-details-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem;
  }

  // On small screens, spans collapse naturally to one column
  .project-detail-item.span-2 {
    grid-column: auto;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .permit-actions-cell {
    .btn {
      font-size: 0.7rem;
      padding: 0.25rem 0.5rem;
    }
  }
}

@media (max-width: 576px) {
  .project-details-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .project-details-header {
    .header-content {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }

    .button-group {
      width: 100%;
      justify-content: flex-end;
    }
  }
}
